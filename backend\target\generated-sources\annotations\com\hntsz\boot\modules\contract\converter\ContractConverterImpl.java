package com.hntsz.boot.modules.contract.converter;

import com.hntsz.boot.modules.contract.model.entity.Contract;
import com.hntsz.boot.modules.contract.model.form.ContractForm;
import com.hntsz.boot.modules.contract.model.vo.ContractVO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:13+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ContractConverterImpl implements ContractConverter {

    @Override
    public ContractVO entityToVo(Contract entity) {
        if ( entity == null ) {
            return null;
        }

        ContractVO contractVO = new ContractVO();

        contractVO.setContractAmount( entity.getContractAmount() );
        contractVO.setContractCategory( entity.getContractCategory() );
        contractVO.setContractName( entity.getContractName() );
        contractVO.setContractNo( entity.getContractNo() );
        contractVO.setContractStatus( entity.getContractStatus() );
        contractVO.setContractType( entity.getContractType() );
        contractVO.setCreateTime( entity.getCreateTime() );
        contractVO.setDeptId( entity.getDeptId() );
        contractVO.setEffectiveDate( entity.getEffectiveDate() );
        contractVO.setExpiryDate( entity.getExpiryDate() );
        contractVO.setId( entity.getId() );
        contractVO.setOpportunityId( entity.getOpportunityId() );
        contractVO.setPaymentMethod( entity.getPaymentMethod() );
        contractVO.setRemark( entity.getRemark() );
        contractVO.setResponsibleUserId( entity.getResponsibleUserId() );
        contractVO.setSigningDate( entity.getSigningDate() );
        contractVO.setSigningLocation( entity.getSigningLocation() );
        contractVO.setUpdateTime( entity.getUpdateTime() );

        return contractVO;
    }

    @Override
    public Contract voToEntity(ContractVO vo) {
        if ( vo == null ) {
            return null;
        }

        Contract contract = new Contract();

        contract.setCreateTime( vo.getCreateTime() );
        contract.setId( vo.getId() );
        contract.setUpdateTime( vo.getUpdateTime() );
        contract.setContractAmount( vo.getContractAmount() );
        contract.setContractCategory( vo.getContractCategory() );
        contract.setContractName( vo.getContractName() );
        contract.setContractNo( vo.getContractNo() );
        contract.setContractStatus( vo.getContractStatus() );
        contract.setContractType( vo.getContractType() );
        contract.setDeptId( vo.getDeptId() );
        contract.setEffectiveDate( vo.getEffectiveDate() );
        contract.setExpiryDate( vo.getExpiryDate() );
        contract.setOpportunityId( vo.getOpportunityId() );
        contract.setPaymentMethod( vo.getPaymentMethod() );
        contract.setRemark( vo.getRemark() );
        contract.setResponsibleUserId( vo.getResponsibleUserId() );
        contract.setSigningDate( vo.getSigningDate() );
        contract.setSigningLocation( vo.getSigningLocation() );

        return contract;
    }

    @Override
    public Contract formToEntity(ContractForm form) {
        if ( form == null ) {
            return null;
        }

        Contract contract = new Contract();

        contract.setId( form.getId() );
        contract.setContractAmount( form.getContractAmount() );
        contract.setContractCategory( form.getContractCategory() );
        contract.setContractName( form.getContractName() );
        contract.setContractNo( form.getContractNo() );
        contract.setContractStatus( form.getContractStatus() );
        contract.setContractType( form.getContractType() );
        contract.setDeptId( form.getDeptId() );
        contract.setEffectiveDate( form.getEffectiveDate() );
        contract.setExpiryDate( form.getExpiryDate() );
        contract.setOpportunityId( form.getOpportunityId() );
        contract.setPaymentMethod( form.getPaymentMethod() );
        contract.setRemark( form.getRemark() );
        contract.setResponsibleUserId( form.getResponsibleUserId() );
        contract.setSigningDate( form.getSigningDate() );
        contract.setSigningLocation( form.getSigningLocation() );

        return contract;
    }

    @Override
    public ContractForm entityToForm(Contract entity) {
        if ( entity == null ) {
            return null;
        }

        ContractForm contractForm = new ContractForm();

        contractForm.setContractAmount( entity.getContractAmount() );
        contractForm.setContractCategory( entity.getContractCategory() );
        contractForm.setContractName( entity.getContractName() );
        contractForm.setContractNo( entity.getContractNo() );
        contractForm.setContractStatus( entity.getContractStatus() );
        contractForm.setContractType( entity.getContractType() );
        contractForm.setDeptId( entity.getDeptId() );
        contractForm.setEffectiveDate( entity.getEffectiveDate() );
        contractForm.setExpiryDate( entity.getExpiryDate() );
        contractForm.setId( entity.getId() );
        contractForm.setOpportunityId( entity.getOpportunityId() );
        contractForm.setPaymentMethod( entity.getPaymentMethod() );
        contractForm.setRemark( entity.getRemark() );
        contractForm.setResponsibleUserId( entity.getResponsibleUserId() );
        contractForm.setSigningDate( entity.getSigningDate() );
        contractForm.setSigningLocation( entity.getSigningLocation() );

        return contractForm;
    }
}
