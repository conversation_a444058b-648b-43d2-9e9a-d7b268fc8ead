package com.hntsz.boot.modules.opportunity.converter;

import com.hntsz.boot.modules.opportunity.model.entity.Opportunity;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityForm;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityVO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:13+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OpportunityConverterImpl implements OpportunityConverter {

    @Override
    public OpportunityVO entityToVo(Opportunity entity) {
        if ( entity == null ) {
            return null;
        }

        OpportunityVO opportunityVO = new OpportunityVO();

        opportunityVO.setActualCloseDate( entity.getActualCloseDate() );
        opportunityVO.setCompetitionInfo( entity.getCompetitionInfo() );
        opportunityVO.setContactEmail( entity.getContactEmail() );
        opportunityVO.setContactPerson( entity.getContactPerson() );
        opportunityVO.setContactPhone( entity.getContactPhone() );
        opportunityVO.setCreateTime( entity.getCreateTime() );
        opportunityVO.setDeptId( entity.getDeptId() );
        opportunityVO.setEstimatedAmount( entity.getEstimatedAmount() );
        opportunityVO.setEstimatedCloseDate( entity.getEstimatedCloseDate() );
        opportunityVO.setId( entity.getId() );
        opportunityVO.setLostReason( entity.getLostReason() );
        opportunityVO.setNextAction( entity.getNextAction() );
        opportunityVO.setNextFollowDate( entity.getNextFollowDate() );
        opportunityVO.setOpportunityCode( entity.getOpportunityCode() );
        opportunityVO.setOpportunityName( entity.getOpportunityName() );
        opportunityVO.setOpportunitySource( entity.getOpportunitySource() );
        opportunityVO.setOpportunityStage( entity.getOpportunityStage() );
        opportunityVO.setOpportunityStatus( entity.getOpportunityStatus() );
        opportunityVO.setOpportunityType( entity.getOpportunityType() );
        opportunityVO.setPartnerId( entity.getPartnerId() );
        opportunityVO.setPriority( entity.getPriority() );
        opportunityVO.setProductInterest( entity.getProductInterest() );
        opportunityVO.setRemark( entity.getRemark() );
        opportunityVO.setRequirements( entity.getRequirements() );
        opportunityVO.setResponsibleUserId( entity.getResponsibleUserId() );
        opportunityVO.setTags( entity.getTags() );
        opportunityVO.setUpdateTime( entity.getUpdateTime() );
        opportunityVO.setWinProbability( entity.getWinProbability() );

        return opportunityVO;
    }

    @Override
    public Opportunity formToEntity(OpportunityForm form) {
        if ( form == null ) {
            return null;
        }

        Opportunity opportunity = new Opportunity();

        opportunity.setId( form.getId() );
        opportunity.setActualCloseDate( form.getActualCloseDate() );
        opportunity.setCompetitionInfo( form.getCompetitionInfo() );
        opportunity.setContactEmail( form.getContactEmail() );
        opportunity.setContactPerson( form.getContactPerson() );
        opportunity.setContactPhone( form.getContactPhone() );
        opportunity.setDeptId( form.getDeptId() );
        opportunity.setEstimatedAmount( form.getEstimatedAmount() );
        opportunity.setEstimatedCloseDate( form.getEstimatedCloseDate() );
        opportunity.setLostReason( form.getLostReason() );
        opportunity.setNextAction( form.getNextAction() );
        opportunity.setNextFollowDate( form.getNextFollowDate() );
        opportunity.setOpportunityCode( form.getOpportunityCode() );
        opportunity.setOpportunityName( form.getOpportunityName() );
        opportunity.setOpportunitySource( form.getOpportunitySource() );
        opportunity.setOpportunityStage( form.getOpportunityStage() );
        opportunity.setOpportunityStatus( form.getOpportunityStatus() );
        opportunity.setOpportunityType( form.getOpportunityType() );
        opportunity.setPartnerId( form.getPartnerId() );
        opportunity.setPriority( form.getPriority() );
        opportunity.setProductInterest( form.getProductInterest() );
        opportunity.setRemark( form.getRemark() );
        opportunity.setRequirements( form.getRequirements() );
        opportunity.setResponsibleUserId( form.getResponsibleUserId() );
        opportunity.setTags( form.getTags() );
        opportunity.setWinProbability( form.getWinProbability() );

        return opportunity;
    }

    @Override
    public OpportunityForm entityToForm(Opportunity entity) {
        if ( entity == null ) {
            return null;
        }

        OpportunityForm opportunityForm = new OpportunityForm();

        opportunityForm.setActualCloseDate( entity.getActualCloseDate() );
        opportunityForm.setCompetitionInfo( entity.getCompetitionInfo() );
        opportunityForm.setContactEmail( entity.getContactEmail() );
        opportunityForm.setContactPerson( entity.getContactPerson() );
        opportunityForm.setContactPhone( entity.getContactPhone() );
        opportunityForm.setDeptId( entity.getDeptId() );
        opportunityForm.setEstimatedAmount( entity.getEstimatedAmount() );
        opportunityForm.setEstimatedCloseDate( entity.getEstimatedCloseDate() );
        opportunityForm.setId( entity.getId() );
        opportunityForm.setLostReason( entity.getLostReason() );
        opportunityForm.setNextAction( entity.getNextAction() );
        opportunityForm.setNextFollowDate( entity.getNextFollowDate() );
        opportunityForm.setOpportunityCode( entity.getOpportunityCode() );
        opportunityForm.setOpportunityName( entity.getOpportunityName() );
        opportunityForm.setOpportunitySource( entity.getOpportunitySource() );
        opportunityForm.setOpportunityStage( entity.getOpportunityStage() );
        opportunityForm.setOpportunityStatus( entity.getOpportunityStatus() );
        opportunityForm.setOpportunityType( entity.getOpportunityType() );
        opportunityForm.setPartnerId( entity.getPartnerId() );
        opportunityForm.setPriority( entity.getPriority() );
        opportunityForm.setProductInterest( entity.getProductInterest() );
        opportunityForm.setRemark( entity.getRemark() );
        opportunityForm.setRequirements( entity.getRequirements() );
        opportunityForm.setResponsibleUserId( entity.getResponsibleUserId() );
        opportunityForm.setTags( entity.getTags() );
        opportunityForm.setWinProbability( entity.getWinProbability() );

        return opportunityForm;
    }
}
