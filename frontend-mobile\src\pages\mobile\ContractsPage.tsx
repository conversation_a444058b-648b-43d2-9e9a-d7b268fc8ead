import { useState, useEffect, useRef, useCallback } from "react";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import {
  Search,
  Filter,
  Plus,
  FileText,
  Calendar,
  DollarSign,
  Building,
  Loader2
} from "lucide-react";
import ContractAPI, { ContractVO, ContractQuery } from "@/api/contract";
import { toast } from "@/components/ui/use-toast";

// 合同状态映射
const CONTRACT_STATUS_MAP = {
  draft: { text: "草稿", color: "secondary" },
  pending: { text: "待签署", color: "warning" },
  active: { text: "已生效", color: "success" },
  completed: { text: "已完成", color: "primary" },
  terminated: { text: "已终止", color: "destructive" },
  cancelled: { text: "已作废", color: "destructive" }
} as const;

// 格式化金额
const formatAmount = (amount: number): string => {
  return `¥${amount.toLocaleString()}`;
};

// 获取主要合作伙伴名称
const getPartnerNames = (parties?: ContractVO['parties']): string[] => {
  if (!parties || parties.length === 0) return ["暂无"];
  return parties.map(p => p.partnerName || "暂无");
};

export default function ContractsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [contracts, setContracts] = useState<ContractVO[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    pending: 0
  });
  const [pageNum, setPageNum] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // 加载合同列表
  const loadContracts = async (query?: string, page: number = 1, append: boolean = false) => {
    try {
      setLoading(true);
      const params: ContractQuery = {
        pageNum: page,
        pageSize: 5,
        keywords: query || undefined
      };

      const result = await ContractAPI.getContractPage(params);
      
      // 更新合同列表
      if (append) {
        setContracts(prev => [...prev, ...result.list]);
      } else {
        setContracts(result.list);
      }

      // 更新是否还有更多数据
      setHasMore(result.list.length === params.pageSize);

      // 只在首次加载或重新搜索时更新统计数据
      if (page === 1) {
        const stats = result.list.reduce((acc, contract) => {
          acc.total++;
          if (contract.contractStatus === 'active') acc.active++;
          if (contract.contractStatus === 'pending') acc.pending++;
          return acc;
        }, { total: 0, active: 0, pending: 0 });
        setStatistics(stats);
      }
    } catch (error) {
      console.error('加载合同列表失败:', error);
      toast({
        variant: "destructive",
        title: "加载失败",
        description: "无法加载合同列表，请稍后重试",
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理滚动加载
  const handleScroll = useCallback(() => {
    if (!containerRef.current || loading || !hasMore) return;

    const container = containerRef.current;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    // 当滚动到距离底部100px时加载更多
    if (scrollHeight - scrollTop - clientHeight < 100) {
      setPageNum(prev => {
        // 只在有更多数据且未加载时才递增
        if (hasMore && !loading) {
          return prev + 1;
        }
        return prev;
      });
    }
  }, [loading, hasMore]);

  // 监听滚动事件
  useEffect(() => {
    if (pageNum === 1) return; 
    loadContracts(searchQuery, pageNum, true);
  }, [pageNum]);

  // 组件挂载时加载数据
  useEffect(() => {
    loadContracts(searchQuery, 1, false);
  }, [searchQuery]);

  // 搜索处理
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPageNum(1);
    setHasMore(true);
  };

  const getStatusColor = (status: string) => {
    return CONTRACT_STATUS_MAP[status as keyof typeof CONTRACT_STATUS_MAP]?.color || "secondary";
  };

  const getStatusText = (status: string) => {
    return CONTRACT_STATUS_MAP[status as keyof typeof CONTRACT_STATUS_MAP]?.text || status;
  };

  const filteredContracts = contracts;

  return (
    <div className="min-h-screen bg-background pb-20" ref={containerRef} style={{ overflowY: 'auto', height: '100vh' }}>
      <MobileHeader
        title="合同管理"
        showSearch={true}
        searchPlaceholder="搜索合同..."
        onSearch={handleSearch}
        searchValue={searchQuery}
      />

      <div className="p-4 space-y-4">
        {/* 统计卡片 */}
        <div className="grid grid-cols-3 gap-3">
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-primary">{statistics.total}</p>
            <p className="text-xs text-muted-foreground">总数</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-success">{statistics.active}</p>
            <p className="text-xs text-muted-foreground">已生效</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-warning">{statistics.pending}</p>
            <p className="text-xs text-muted-foreground">待签署</p>
          </div>
        </div>

        {/* 合同列表 */}
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">加载中...</span>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredContracts.map((contract) => (
              <div
                key={contract.id}
                className="bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => {
                  navigate(`/contracts/${contract.id}`);
                }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <FileText className="h-4 w-4 text-primary" />
                      <h3 className="font-semibold text-sm">{contract.contractName}</h3>
                    </div>
                    <p className="text-xs text-muted-foreground mb-1">
                      编号: {contract.contractNo}
                    </p>
                  </div>
                  <Badge variant={getStatusColor(contract.contractStatus) as any}>
                    {getStatusText(contract.contractStatus)}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="space-y-1">
                    {getPartnerNames(contract.parties).map((name, idx) => (
                      <div key={idx} className="flex items-center gap-2 text-sm">
                        <Building className="h-3 w-3 text-muted-foreground" />
                        <span className="text-muted-foreground">{name}</span>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-3 w-3 text-muted-foreground" />
                    <span className="font-medium text-primary">{formatAmount(contract.contractAmount)}</span>
                  </div>

                  {contract.signingDate && (
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">
                        {contract.signingDate} - {contract.expiryDate}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {!loading && filteredContracts.length === 0 && (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">未找到相关合同</p>
          </div>
        )}
      </div>

      {/* 合同列表底部加载状态 */}
      {loading && (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2 text-muted-foreground">加载中...</span>
        </div>
      )}

      {!loading && !hasMore && contracts.length > 0 && (
        <div className="text-center py-4 text-muted-foreground">
          没有更多数据了
        </div>
      )}

      <BottomNavigation />
    </div>
  );
}