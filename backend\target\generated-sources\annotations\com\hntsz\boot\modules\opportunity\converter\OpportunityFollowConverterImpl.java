package com.hntsz.boot.modules.opportunity.converter;

import com.hntsz.boot.modules.opportunity.model.entity.OpportunityFollow;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityFollowForm;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:14+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OpportunityFollowConverterImpl implements OpportunityFollowConverter {

    @Override
    public OpportunityFollowVO entityToVo(OpportunityFollow entity) {
        if ( entity == null ) {
            return null;
        }

        OpportunityFollowVO opportunityFollowVO = new OpportunityFollowVO();

        opportunityFollowVO.setAttachmentId( entity.getAttachmentId() );
        opportunityFollowVO.setContactPerson( entity.getContactPerson() );
        opportunityFollowVO.setCreateTime( entity.getCreateTime() );
        opportunityFollowVO.setFollowContent( entity.getFollowContent() );
        opportunityFollowVO.setFollowDate( entity.getFollowDate() );
        opportunityFollowVO.setFollowDuration( entity.getFollowDuration() );
        opportunityFollowVO.setFollowResult( entity.getFollowResult() );
        opportunityFollowVO.setFollowType( entity.getFollowType() );
        opportunityFollowVO.setFollowUserId( entity.getFollowUserId() );
        opportunityFollowVO.setId( entity.getId() );
        opportunityFollowVO.setNextAction( entity.getNextAction() );
        opportunityFollowVO.setNextFollowDate( entity.getNextFollowDate() );
        opportunityFollowVO.setOpportunityId( entity.getOpportunityId() );
        opportunityFollowVO.setRemark( entity.getRemark() );
        opportunityFollowVO.setUpdateTime( entity.getUpdateTime() );

        return opportunityFollowVO;
    }

    @Override
    public OpportunityFollow formToEntity(OpportunityFollowForm form) {
        if ( form == null ) {
            return null;
        }

        OpportunityFollow opportunityFollow = new OpportunityFollow();

        opportunityFollow.setId( form.getId() );
        opportunityFollow.setAttachmentId( form.getAttachmentId() );
        opportunityFollow.setContactPerson( form.getContactPerson() );
        opportunityFollow.setFollowContent( form.getFollowContent() );
        opportunityFollow.setFollowDate( form.getFollowDate() );
        opportunityFollow.setFollowDuration( form.getFollowDuration() );
        opportunityFollow.setFollowResult( form.getFollowResult() );
        opportunityFollow.setFollowType( form.getFollowType() );
        opportunityFollow.setFollowUserId( form.getFollowUserId() );
        opportunityFollow.setNextAction( form.getNextAction() );
        opportunityFollow.setNextFollowDate( form.getNextFollowDate() );
        opportunityFollow.setOpportunityId( form.getOpportunityId() );
        opportunityFollow.setRemark( form.getRemark() );

        return opportunityFollow;
    }

    @Override
    public OpportunityFollowForm entityToForm(OpportunityFollow entity) {
        if ( entity == null ) {
            return null;
        }

        OpportunityFollowForm opportunityFollowForm = new OpportunityFollowForm();

        opportunityFollowForm.setAttachmentId( entity.getAttachmentId() );
        opportunityFollowForm.setContactPerson( entity.getContactPerson() );
        opportunityFollowForm.setFollowContent( entity.getFollowContent() );
        opportunityFollowForm.setFollowDate( entity.getFollowDate() );
        opportunityFollowForm.setFollowDuration( entity.getFollowDuration() );
        opportunityFollowForm.setFollowResult( entity.getFollowResult() );
        opportunityFollowForm.setFollowType( entity.getFollowType() );
        opportunityFollowForm.setFollowUserId( entity.getFollowUserId() );
        opportunityFollowForm.setId( entity.getId() );
        opportunityFollowForm.setNextAction( entity.getNextAction() );
        opportunityFollowForm.setNextFollowDate( entity.getNextFollowDate() );
        opportunityFollowForm.setOpportunityId( entity.getOpportunityId() );
        opportunityFollowForm.setRemark( entity.getRemark() );

        return opportunityFollowForm;
    }
}
