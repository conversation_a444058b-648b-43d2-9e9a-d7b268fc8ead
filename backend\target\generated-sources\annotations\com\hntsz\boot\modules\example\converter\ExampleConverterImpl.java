package com.hntsz.boot.modules.example.converter;

import com.hntsz.boot.modules.example.model.entity.Example;
import com.hntsz.boot.modules.example.model.form.ExampleForm;
import com.hntsz.boot.modules.example.model.vo.ExampleVO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:14+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ExampleConverterImpl implements ExampleConverter {

    @Override
    public ExampleVO entityToVo(Example entity) {
        if ( entity == null ) {
            return null;
        }

        ExampleVO exampleVO = new ExampleVO();

        exampleVO.setAge( entity.getAge() );
        exampleVO.setName( entity.getName() );

        return exampleVO;
    }

    @Override
    public ExampleVO entityToDetail(Example entity) {
        if ( entity == null ) {
            return null;
        }

        ExampleVO exampleVO = new ExampleVO();

        exampleVO.setAge( entity.getAge() );
        exampleVO.setName( entity.getName() );

        return exampleVO;
    }

    @Override
    public Example voToEntity(ExampleVO vo) {
        if ( vo == null ) {
            return null;
        }

        Example example = new Example();

        example.setAge( vo.getAge() );
        example.setName( vo.getName() );

        return example;
    }

    @Override
    public Example createFormToEntity(ExampleForm form) {
        if ( form == null ) {
            return null;
        }

        Example example = new Example();

        example.setAge( form.getAge() );
        example.setName( form.getName() );

        return example;
    }

    @Override
    public Example updateFormToEntity(ExampleForm form) {
        if ( form == null ) {
            return null;
        }

        Example example = new Example();

        example.setAge( form.getAge() );
        example.setName( form.getName() );

        return example;
    }
}
