package com.hntsz.boot.modules.contract.converter;

import com.hntsz.boot.modules.contract.model.entity.Partner;
import com.hntsz.boot.modules.contract.model.form.PartnerForm;
import com.hntsz.boot.modules.contract.model.vo.PartnerVO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:14+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class PartnerConverterImpl implements PartnerConverter {

    @Override
    public PartnerVO entityToVO(Partner entity) {
        if ( entity == null ) {
            return null;
        }

        PartnerVO partnerVO = new PartnerVO();

        partnerVO.setAddress( entity.getAddress() );
        partnerVO.setBankAccount( entity.getBankAccount() );
        partnerVO.setBankName( entity.getBankName() );
        partnerVO.setCertificateNumber( entity.getCertificateNumber() );
        partnerVO.setCertificateType( entity.getCertificateType() );
        partnerVO.setContactEmail( entity.getContactEmail() );
        partnerVO.setContactPerson( entity.getContactPerson() );
        partnerVO.setContactPhone( entity.getContactPhone() );
        partnerVO.setCreateTime( entity.getCreateTime() );
        partnerVO.setId( entity.getId() );
        partnerVO.setIsOurCompany( entity.getIsOurCompany() );
        partnerVO.setLegalRepresentative( entity.getLegalRepresentative() );
        partnerVO.setPartnerCode( entity.getPartnerCode() );
        partnerVO.setPartnerName( entity.getPartnerName() );
        partnerVO.setPartnerType( entity.getPartnerType() );
        partnerVO.setRemark( entity.getRemark() );
        partnerVO.setStatus( entity.getStatus() );
        partnerVO.setTaxNumber( entity.getTaxNumber() );
        partnerVO.setUpdateTime( entity.getUpdateTime() );

        return partnerVO;
    }

    @Override
    public Partner voToEntity(PartnerVO vo) {
        if ( vo == null ) {
            return null;
        }

        Partner partner = new Partner();

        partner.setCreateTime( vo.getCreateTime() );
        partner.setId( vo.getId() );
        partner.setUpdateTime( vo.getUpdateTime() );
        partner.setAddress( vo.getAddress() );
        partner.setBankAccount( vo.getBankAccount() );
        partner.setBankName( vo.getBankName() );
        partner.setCertificateNumber( vo.getCertificateNumber() );
        partner.setCertificateType( vo.getCertificateType() );
        partner.setContactEmail( vo.getContactEmail() );
        partner.setContactPerson( vo.getContactPerson() );
        partner.setContactPhone( vo.getContactPhone() );
        partner.setIsOurCompany( vo.getIsOurCompany() );
        partner.setLegalRepresentative( vo.getLegalRepresentative() );
        partner.setPartnerCode( vo.getPartnerCode() );
        partner.setPartnerName( vo.getPartnerName() );
        partner.setPartnerType( vo.getPartnerType() );
        partner.setRemark( vo.getRemark() );
        partner.setStatus( vo.getStatus() );
        partner.setTaxNumber( vo.getTaxNumber() );

        return partner;
    }

    @Override
    public Partner formToEntity(PartnerForm form) {
        if ( form == null ) {
            return null;
        }

        Partner partner = new Partner();

        partner.setId( form.getId() );
        partner.setAddress( form.getAddress() );
        partner.setBankAccount( form.getBankAccount() );
        partner.setBankName( form.getBankName() );
        partner.setCertificateNumber( form.getCertificateNumber() );
        partner.setCertificateType( form.getCertificateType() );
        partner.setContactEmail( form.getContactEmail() );
        partner.setContactPerson( form.getContactPerson() );
        partner.setContactPhone( form.getContactPhone() );
        partner.setIsOurCompany( form.getIsOurCompany() );
        partner.setLegalRepresentative( form.getLegalRepresentative() );
        partner.setPartnerCode( form.getPartnerCode() );
        partner.setPartnerName( form.getPartnerName() );
        partner.setPartnerType( form.getPartnerType() );
        partner.setRemark( form.getRemark() );
        partner.setStatus( form.getStatus() );
        partner.setTaxNumber( form.getTaxNumber() );

        return partner;
    }

    @Override
    public PartnerForm entityToForm(Partner entity) {
        if ( entity == null ) {
            return null;
        }

        PartnerForm partnerForm = new PartnerForm();

        partnerForm.setAddress( entity.getAddress() );
        partnerForm.setBankAccount( entity.getBankAccount() );
        partnerForm.setBankName( entity.getBankName() );
        partnerForm.setCertificateNumber( entity.getCertificateNumber() );
        partnerForm.setCertificateType( entity.getCertificateType() );
        partnerForm.setContactEmail( entity.getContactEmail() );
        partnerForm.setContactPerson( entity.getContactPerson() );
        partnerForm.setContactPhone( entity.getContactPhone() );
        partnerForm.setId( entity.getId() );
        partnerForm.setIsOurCompany( entity.getIsOurCompany() );
        partnerForm.setLegalRepresentative( entity.getLegalRepresentative() );
        partnerForm.setPartnerCode( entity.getPartnerCode() );
        partnerForm.setPartnerName( entity.getPartnerName() );
        partnerForm.setPartnerType( entity.getPartnerType() );
        partnerForm.setRemark( entity.getRemark() );
        partnerForm.setStatus( entity.getStatus() );
        partnerForm.setTaxNumber( entity.getTaxNumber() );

        return partnerForm;
    }
}
