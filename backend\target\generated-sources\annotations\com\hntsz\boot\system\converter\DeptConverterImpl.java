package com.hntsz.boot.system.converter;

import com.hntsz.boot.system.model.entity.Dept;
import com.hntsz.boot.system.model.form.DeptForm;
import com.hntsz.boot.system.model.vo.DeptVO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:13+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DeptConverterImpl implements DeptConverter {

    @Override
    public DeptForm toForm(Dept entity) {
        if ( entity == null ) {
            return null;
        }

        DeptForm deptForm = new DeptForm();

        deptForm.setCode( entity.getCode() );
        deptForm.setId( entity.getId() );
        deptForm.setName( entity.getName() );
        deptForm.setParentId( entity.getParentId() );
        deptForm.setSort( entity.getSort() );
        deptForm.setStatus( entity.getStatus() );

        return deptForm;
    }

    @Override
    public DeptVO toVo(Dept entity) {
        if ( entity == null ) {
            return null;
        }

        DeptVO deptVO = new DeptVO();

        deptVO.setCode( entity.getCode() );
        deptVO.setCreateTime( entity.getCreateTime() );
        deptVO.setId( entity.getId() );
        deptVO.setName( entity.getName() );
        deptVO.setParentId( entity.getParentId() );
        deptVO.setSort( entity.getSort() );
        deptVO.setStatus( entity.getStatus() );
        deptVO.setUpdateTime( entity.getUpdateTime() );

        return deptVO;
    }

    @Override
    public Dept toEntity(DeptForm deptForm) {
        if ( deptForm == null ) {
            return null;
        }

        Dept dept = new Dept();

        dept.setId( deptForm.getId() );
        dept.setCode( deptForm.getCode() );
        dept.setName( deptForm.getName() );
        dept.setParentId( deptForm.getParentId() );
        dept.setSort( deptForm.getSort() );
        dept.setStatus( deptForm.getStatus() );

        return dept;
    }
}
