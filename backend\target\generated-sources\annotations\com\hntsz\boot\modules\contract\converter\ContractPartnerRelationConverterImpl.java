package com.hntsz.boot.modules.contract.converter;

import com.hntsz.boot.modules.contract.model.entity.ContractPartnerRelation;
import com.hntsz.boot.modules.contract.model.form.ContractPartnerRelationForm;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:14+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class ContractPartnerRelationConverterImpl implements ContractPartnerRelationConverter {

    @Override
    public ContractPartnerRelationVO entityToVO(ContractPartnerRelation entity) {
        if ( entity == null ) {
            return null;
        }

        ContractPartnerRelationVO contractPartnerRelationVO = new ContractPartnerRelationVO();

        contractPartnerRelationVO.setContractId( entity.getContractId() );
        contractPartnerRelationVO.setCreateTime( entity.getCreateTime() );
        contractPartnerRelationVO.setId( entity.getId() );
        contractPartnerRelationVO.setPartnerId( entity.getPartnerId() );
        contractPartnerRelationVO.setPartnerRole( entity.getPartnerRole() );
        contractPartnerRelationVO.setPartnerRoleDesc( entity.getPartnerRoleDesc() );
        contractPartnerRelationVO.setRemark( entity.getRemark() );
        contractPartnerRelationVO.setSigningDate( entity.getSigningDate() );
        contractPartnerRelationVO.setSigningPerson( entity.getSigningPerson() );
        contractPartnerRelationVO.setSigningPersonTitle( entity.getSigningPersonTitle() );
        contractPartnerRelationVO.setSort( entity.getSort() );
        contractPartnerRelationVO.setUpdateTime( entity.getUpdateTime() );

        return contractPartnerRelationVO;
    }

    @Override
    public ContractPartnerRelation voToEntity(ContractPartnerRelationVO vo) {
        if ( vo == null ) {
            return null;
        }

        ContractPartnerRelation contractPartnerRelation = new ContractPartnerRelation();

        contractPartnerRelation.setCreateTime( vo.getCreateTime() );
        contractPartnerRelation.setId( vo.getId() );
        contractPartnerRelation.setUpdateTime( vo.getUpdateTime() );
        contractPartnerRelation.setContractId( vo.getContractId() );
        contractPartnerRelation.setPartnerId( vo.getPartnerId() );
        contractPartnerRelation.setPartnerRole( vo.getPartnerRole() );
        contractPartnerRelation.setPartnerRoleDesc( vo.getPartnerRoleDesc() );
        contractPartnerRelation.setRemark( vo.getRemark() );
        contractPartnerRelation.setSigningDate( vo.getSigningDate() );
        contractPartnerRelation.setSigningPerson( vo.getSigningPerson() );
        contractPartnerRelation.setSigningPersonTitle( vo.getSigningPersonTitle() );
        contractPartnerRelation.setSort( vo.getSort() );

        return contractPartnerRelation;
    }

    @Override
    public ContractPartnerRelation formToEntity(ContractPartnerRelationForm form) {
        if ( form == null ) {
            return null;
        }

        ContractPartnerRelation contractPartnerRelation = new ContractPartnerRelation();

        contractPartnerRelation.setId( form.getId() );
        contractPartnerRelation.setContractId( form.getContractId() );
        contractPartnerRelation.setPartnerId( form.getPartnerId() );
        contractPartnerRelation.setPartnerRole( form.getPartnerRole() );
        contractPartnerRelation.setPartnerRoleDesc( form.getPartnerRoleDesc() );
        contractPartnerRelation.setRemark( form.getRemark() );
        contractPartnerRelation.setSigningDate( form.getSigningDate() );
        contractPartnerRelation.setSigningPerson( form.getSigningPerson() );
        contractPartnerRelation.setSigningPersonTitle( form.getSigningPersonTitle() );
        contractPartnerRelation.setSort( form.getSort() );

        return contractPartnerRelation;
    }

    @Override
    public ContractPartnerRelationForm entityToForm(ContractPartnerRelation entity) {
        if ( entity == null ) {
            return null;
        }

        ContractPartnerRelationForm contractPartnerRelationForm = new ContractPartnerRelationForm();

        contractPartnerRelationForm.setContractId( entity.getContractId() );
        contractPartnerRelationForm.setId( entity.getId() );
        contractPartnerRelationForm.setPartnerId( entity.getPartnerId() );
        contractPartnerRelationForm.setPartnerRole( entity.getPartnerRole() );
        contractPartnerRelationForm.setPartnerRoleDesc( entity.getPartnerRoleDesc() );
        contractPartnerRelationForm.setRemark( entity.getRemark() );
        contractPartnerRelationForm.setSigningDate( entity.getSigningDate() );
        contractPartnerRelationForm.setSigningPerson( entity.getSigningPerson() );
        contractPartnerRelationForm.setSigningPersonTitle( entity.getSigningPersonTitle() );
        contractPartnerRelationForm.setSort( entity.getSort() );

        return contractPartnerRelationForm;
    }
}
