package com.hntsz.boot.system.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.system.model.bo.UserBO;
import com.hntsz.boot.system.model.dto.CurrentUserDTO;
import com.hntsz.boot.system.model.dto.UserImportDTO;
import com.hntsz.boot.system.model.entity.User;
import com.hntsz.boot.system.model.form.UserForm;
import com.hntsz.boot.system.model.form.UserProfileForm;
import com.hntsz.boot.system.model.vo.UserPageVO;
import com.hntsz.boot.system.model.vo.UserProfileVO;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:13+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserConverterImpl implements UserConverter {

    @Override
    public UserPageVO toPageVo(UserBO bo) {
        if ( bo == null ) {
            return null;
        }

        UserPageVO userPageVO = new UserPageVO();

        userPageVO.setAvatar( bo.getAvatar() );
        userPageVO.setCreateTime( bo.getCreateTime() );
        userPageVO.setDeptName( bo.getDeptName() );
        userPageVO.setEmail( bo.getEmail() );
        userPageVO.setGender( bo.getGender() );
        userPageVO.setId( bo.getId() );
        userPageVO.setMobile( bo.getMobile() );
        userPageVO.setNickname( bo.getNickname() );
        userPageVO.setRoleNames( bo.getRoleNames() );
        userPageVO.setStatus( bo.getStatus() );
        userPageVO.setUsername( bo.getUsername() );

        return userPageVO;
    }

    @Override
    public Page<UserPageVO> toPageVo(Page<UserBO> bo) {
        if ( bo == null ) {
            return null;
        }

        Page<UserPageVO> page = new Page<UserPageVO>();

        page.setPages( bo.getPages() );
        page.setCurrent( bo.getCurrent() );
        page.setRecords( userBOListToUserPageVOList( bo.getRecords() ) );
        page.setSize( bo.getSize() );
        page.setTotal( bo.getTotal() );

        return page;
    }

    @Override
    public UserForm toForm(User entity) {
        if ( entity == null ) {
            return null;
        }

        UserForm userForm = new UserForm();

        userForm.setAvatar( entity.getAvatar() );
        userForm.setDeptId( entity.getDeptId() );
        userForm.setEmail( entity.getEmail() );
        userForm.setGender( entity.getGender() );
        userForm.setId( entity.getId() );
        userForm.setMobile( entity.getMobile() );
        userForm.setNickname( entity.getNickname() );
        userForm.setStatus( entity.getStatus() );
        userForm.setUsername( entity.getUsername() );

        return userForm;
    }

    @Override
    public User toEntity(UserForm entity) {
        if ( entity == null ) {
            return null;
        }

        User user = new User();

        user.setId( entity.getId() );
        user.setAvatar( entity.getAvatar() );
        user.setDeptId( entity.getDeptId() );
        user.setEmail( entity.getEmail() );
        user.setGender( entity.getGender() );
        user.setMobile( entity.getMobile() );
        user.setNickname( entity.getNickname() );
        user.setStatus( entity.getStatus() );
        user.setUsername( entity.getUsername() );

        return user;
    }

    @Override
    public CurrentUserDTO toCurrentUserDto(User entity) {
        if ( entity == null ) {
            return null;
        }

        CurrentUserDTO currentUserDTO = new CurrentUserDTO();

        currentUserDTO.setUserId( entity.getId() );
        currentUserDTO.setAvatar( entity.getAvatar() );
        currentUserDTO.setNickname( entity.getNickname() );
        currentUserDTO.setUsername( entity.getUsername() );

        return currentUserDTO;
    }

    @Override
    public User toEntity(UserImportDTO vo) {
        if ( vo == null ) {
            return null;
        }

        User user = new User();

        user.setEmail( vo.getEmail() );
        user.setMobile( vo.getMobile() );
        user.setNickname( vo.getNickname() );
        user.setUsername( vo.getUsername() );

        return user;
    }

    @Override
    public UserProfileVO toProfileVo(UserBO bo) {
        if ( bo == null ) {
            return null;
        }

        UserProfileVO userProfileVO = new UserProfileVO();

        userProfileVO.setAvatar( bo.getAvatar() );
        if ( bo.getCreateTime() != null ) {
            userProfileVO.setCreateTime( Date.from( bo.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        userProfileVO.setDeptName( bo.getDeptName() );
        userProfileVO.setEmail( bo.getEmail() );
        userProfileVO.setGender( bo.getGender() );
        userProfileVO.setId( bo.getId() );
        userProfileVO.setMobile( bo.getMobile() );
        userProfileVO.setNickname( bo.getNickname() );
        userProfileVO.setRoleNames( bo.getRoleNames() );
        userProfileVO.setUsername( bo.getUsername() );

        return userProfileVO;
    }

    @Override
    public User toEntity(UserProfileForm formData) {
        if ( formData == null ) {
            return null;
        }

        User user = new User();

        user.setId( formData.getId() );
        user.setAvatar( formData.getAvatar() );
        user.setEmail( formData.getEmail() );
        user.setGender( formData.getGender() );
        user.setMobile( formData.getMobile() );
        user.setNickname( formData.getNickname() );
        user.setUsername( formData.getUsername() );

        return user;
    }

    @Override
    public Option<String> toOption(User entity) {
        if ( entity == null ) {
            return null;
        }

        Option<String> option = new Option<String>();

        option.setLabel( entity.getNickname() );
        if ( entity.getId() != null ) {
            option.setValue( String.valueOf( entity.getId() ) );
        }

        return option;
    }

    @Override
    public List<Option<String>> toOptions(List<User> list) {
        if ( list == null ) {
            return null;
        }

        List<Option<String>> list1 = new ArrayList<Option<String>>( list.size() );
        for ( User user : list ) {
            list1.add( toOption( user ) );
        }

        return list1;
    }

    protected List<UserPageVO> userBOListToUserPageVOList(List<UserBO> list) {
        if ( list == null ) {
            return null;
        }

        List<UserPageVO> list1 = new ArrayList<UserPageVO>( list.size() );
        for ( UserBO userBO : list ) {
            list1.add( toPageVo( userBO ) );
        }

        return list1;
    }
}
