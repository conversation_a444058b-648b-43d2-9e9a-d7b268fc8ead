package com.hntsz.boot.system.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.system.model.entity.Config;
import com.hntsz.boot.system.model.form.ConfigForm;
import com.hntsz.boot.system.model.vo.ConfigVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:14+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ConfigConverterImpl implements ConfigConverter {

    @Override
    public Page<ConfigVO> toPageVo(Page<Config> page) {
        if ( page == null ) {
            return null;
        }

        Page<ConfigVO> page1 = new Page<ConfigVO>();

        page1.setPages( page.getPages() );
        page1.setCurrent( page.getCurrent() );
        page1.setRecords( configListToConfigVOList( page.getRecords() ) );
        page1.setSize( page.getSize() );
        page1.setTotal( page.getTotal() );

        return page1;
    }

    @Override
    public Config toEntity(ConfigForm configForm) {
        if ( configForm == null ) {
            return null;
        }

        Config config = new Config();

        config.setId( configForm.getId() );
        config.setConfigKey( configForm.getConfigKey() );
        config.setConfigName( configForm.getConfigName() );
        config.setConfigValue( configForm.getConfigValue() );
        config.setRemark( configForm.getRemark() );

        return config;
    }

    @Override
    public ConfigForm toForm(Config entity) {
        if ( entity == null ) {
            return null;
        }

        ConfigForm configForm = new ConfigForm();

        configForm.setConfigKey( entity.getConfigKey() );
        configForm.setConfigName( entity.getConfigName() );
        configForm.setConfigValue( entity.getConfigValue() );
        configForm.setId( entity.getId() );
        configForm.setRemark( entity.getRemark() );

        return configForm;
    }

    protected ConfigVO configToConfigVO(Config config) {
        if ( config == null ) {
            return null;
        }

        ConfigVO.ConfigVOBuilder configVO = ConfigVO.builder();

        configVO.configKey( config.getConfigKey() );
        configVO.configName( config.getConfigName() );
        configVO.configValue( config.getConfigValue() );
        configVO.id( config.getId() );
        configVO.remark( config.getRemark() );

        return configVO.build();
    }

    protected List<ConfigVO> configListToConfigVOList(List<Config> list) {
        if ( list == null ) {
            return null;
        }

        List<ConfigVO> list1 = new ArrayList<ConfigVO>( list.size() );
        for ( Config config : list ) {
            list1.add( configToConfigVO( config ) );
        }

        return list1;
    }
}
