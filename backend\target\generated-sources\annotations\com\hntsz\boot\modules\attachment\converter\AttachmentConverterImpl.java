package com.hntsz.boot.modules.attachment.converter;

import com.hntsz.boot.modules.attachment.model.entity.Attachment;
import com.hntsz.boot.modules.attachment.model.vo.AttachmentVO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:13+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AttachmentConverterImpl implements AttachmentConverter {

    @Override
    public AttachmentVO entityToVo(Attachment entity) {
        if ( entity == null ) {
            return null;
        }

        AttachmentVO attachmentVO = new AttachmentVO();

        attachmentVO.setCreateTime( entity.getCreateTime() );
        attachmentVO.setFileName( entity.getFileName() );
        attachmentVO.setFilePath( entity.getFilePath() );
        attachmentVO.setFileSize( entity.getFileSize() );
        attachmentVO.setFileType( entity.getFileType() );
        attachmentVO.setId( entity.getId() );
        attachmentVO.setUpdateTime( entity.getUpdateTime() );

        return attachmentVO;
    }

    @Override
    public AttachmentVO entityToDetail(Attachment entity) {
        if ( entity == null ) {
            return null;
        }

        AttachmentVO attachmentVO = new AttachmentVO();

        attachmentVO.setCreateTime( entity.getCreateTime() );
        attachmentVO.setFileName( entity.getFileName() );
        attachmentVO.setFilePath( entity.getFilePath() );
        attachmentVO.setFileSize( entity.getFileSize() );
        attachmentVO.setFileType( entity.getFileType() );
        attachmentVO.setId( entity.getId() );
        attachmentVO.setUpdateTime( entity.getUpdateTime() );

        return attachmentVO;
    }

    @Override
    public Attachment voToEntity(AttachmentVO vo) {
        if ( vo == null ) {
            return null;
        }

        Attachment attachment = new Attachment();

        attachment.setCreateTime( vo.getCreateTime() );
        attachment.setId( vo.getId() );
        attachment.setUpdateTime( vo.getUpdateTime() );
        attachment.setFileName( vo.getFileName() );
        attachment.setFilePath( vo.getFilePath() );
        attachment.setFileSize( vo.getFileSize() );
        attachment.setFileType( vo.getFileType() );

        return attachment;
    }

    @Override
    public Attachment createFormToEntity(AttachmentVO form) {
        if ( form == null ) {
            return null;
        }

        Attachment attachment = new Attachment();

        attachment.setCreateTime( form.getCreateTime() );
        attachment.setId( form.getId() );
        attachment.setUpdateTime( form.getUpdateTime() );
        attachment.setFileName( form.getFileName() );
        attachment.setFilePath( form.getFilePath() );
        attachment.setFileSize( form.getFileSize() );
        attachment.setFileType( form.getFileType() );

        return attachment;
    }

    @Override
    public Attachment updateFormToEntity(AttachmentVO form) {
        if ( form == null ) {
            return null;
        }

        Attachment attachment = new Attachment();

        attachment.setCreateTime( form.getCreateTime() );
        attachment.setId( form.getId() );
        attachment.setUpdateTime( form.getUpdateTime() );
        attachment.setFileName( form.getFileName() );
        attachment.setFilePath( form.getFilePath() );
        attachment.setFileSize( form.getFileSize() );
        attachment.setFileType( form.getFileType() );

        return attachment;
    }
}
