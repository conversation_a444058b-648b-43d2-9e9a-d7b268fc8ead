package com.hntsz.boot.shared.codegen.converter;

import com.hntsz.boot.shared.codegen.model.entity.GenConfig;
import com.hntsz.boot.shared.codegen.model.entity.GenFieldConfig;
import com.hntsz.boot.shared.codegen.model.form.GenConfigForm;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-30T17:27:14+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CodegenConverterImpl implements CodegenConverter {

    @Override
    public GenConfigForm toGenConfigForm(GenConfig genConfig, List<GenFieldConfig> fieldConfigs) {
        if ( genConfig == null && fieldConfigs == null ) {
            return null;
        }

        GenConfigForm genConfigForm = new GenConfigForm();

        if ( genConfig != null ) {
            genConfigForm.setTableName( genConfig.getTableName() );
            genConfigForm.setBusinessName( genConfig.getBusinessName() );
            genConfigForm.setModuleName( genConfig.getModuleName() );
            genConfigForm.setPackageName( genConfig.getPackageName() );
            genConfigForm.setEntityName( genConfig.getEntityName() );
            genConfigForm.setAuthor( genConfig.getAuthor() );
            genConfigForm.setId( genConfig.getId() );
            genConfigForm.setParentMenuId( genConfig.getParentMenuId() );
        }
        genConfigForm.setFieldConfigs( toGenFieldConfigForm( fieldConfigs ) );

        return genConfigForm;
    }

    @Override
    public List<GenConfigForm.FieldConfig> toGenFieldConfigForm(List<GenFieldConfig> fieldConfigs) {
        if ( fieldConfigs == null ) {
            return null;
        }

        List<GenConfigForm.FieldConfig> list = new ArrayList<GenConfigForm.FieldConfig>( fieldConfigs.size() );
        for ( GenFieldConfig genFieldConfig : fieldConfigs ) {
            list.add( toGenFieldConfigForm( genFieldConfig ) );
        }

        return list;
    }

    @Override
    public GenConfigForm.FieldConfig toGenFieldConfigForm(GenFieldConfig genFieldConfig) {
        if ( genFieldConfig == null ) {
            return null;
        }

        GenConfigForm.FieldConfig fieldConfig = new GenConfigForm.FieldConfig();

        fieldConfig.setColumnName( genFieldConfig.getColumnName() );
        fieldConfig.setColumnType( genFieldConfig.getColumnType() );
        fieldConfig.setDictType( genFieldConfig.getDictType() );
        fieldConfig.setFieldComment( genFieldConfig.getFieldComment() );
        fieldConfig.setFieldName( genFieldConfig.getFieldName() );
        fieldConfig.setFieldSort( genFieldConfig.getFieldSort() );
        fieldConfig.setFieldType( genFieldConfig.getFieldType() );
        fieldConfig.setFormType( genFieldConfig.getFormType() );
        fieldConfig.setId( genFieldConfig.getId() );
        fieldConfig.setIsRequired( genFieldConfig.getIsRequired() );
        fieldConfig.setIsShowInForm( genFieldConfig.getIsShowInForm() );
        fieldConfig.setIsShowInList( genFieldConfig.getIsShowInList() );
        fieldConfig.setIsShowInQuery( genFieldConfig.getIsShowInQuery() );
        if ( genFieldConfig.getMaxLength() != null ) {
            fieldConfig.setMaxLength( genFieldConfig.getMaxLength().intValue() );
        }
        fieldConfig.setQueryType( genFieldConfig.getQueryType() );

        return fieldConfig;
    }

    @Override
    public GenConfig toGenConfig(GenConfigForm formData) {
        if ( formData == null ) {
            return null;
        }

        GenConfig genConfig = new GenConfig();

        genConfig.setId( formData.getId() );
        genConfig.setAuthor( formData.getAuthor() );
        genConfig.setBusinessName( formData.getBusinessName() );
        genConfig.setEntityName( formData.getEntityName() );
        genConfig.setModuleName( formData.getModuleName() );
        genConfig.setPackageName( formData.getPackageName() );
        genConfig.setParentMenuId( formData.getParentMenuId() );
        genConfig.setTableName( formData.getTableName() );

        return genConfig;
    }

    @Override
    public List<GenFieldConfig> toGenFieldConfig(List<GenConfigForm.FieldConfig> fieldConfigs) {
        if ( fieldConfigs == null ) {
            return null;
        }

        List<GenFieldConfig> list = new ArrayList<GenFieldConfig>( fieldConfigs.size() );
        for ( GenConfigForm.FieldConfig fieldConfig : fieldConfigs ) {
            list.add( toGenFieldConfig( fieldConfig ) );
        }

        return list;
    }

    @Override
    public GenFieldConfig toGenFieldConfig(GenConfigForm.FieldConfig fieldConfig) {
        if ( fieldConfig == null ) {
            return null;
        }

        GenFieldConfig genFieldConfig = new GenFieldConfig();

        genFieldConfig.setId( fieldConfig.getId() );
        genFieldConfig.setColumnName( fieldConfig.getColumnName() );
        genFieldConfig.setColumnType( fieldConfig.getColumnType() );
        genFieldConfig.setDictType( fieldConfig.getDictType() );
        genFieldConfig.setFieldComment( fieldConfig.getFieldComment() );
        genFieldConfig.setFieldName( fieldConfig.getFieldName() );
        genFieldConfig.setFieldSort( fieldConfig.getFieldSort() );
        genFieldConfig.setFieldType( fieldConfig.getFieldType() );
        genFieldConfig.setFormType( fieldConfig.getFormType() );
        genFieldConfig.setIsRequired( fieldConfig.getIsRequired() );
        genFieldConfig.setIsShowInForm( fieldConfig.getIsShowInForm() );
        genFieldConfig.setIsShowInList( fieldConfig.getIsShowInList() );
        genFieldConfig.setIsShowInQuery( fieldConfig.getIsShowInQuery() );
        if ( fieldConfig.getMaxLength() != null ) {
            genFieldConfig.setMaxLength( fieldConfig.getMaxLength().longValue() );
        }
        genFieldConfig.setQueryType( fieldConfig.getQueryType() );

        return genFieldConfig;
    }
}
